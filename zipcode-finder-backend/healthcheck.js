#!/usr/bin/env node

// Simple health check script for deployment verification
const http = require('http');

const PORT = process.env.PORT || 10000;
const HOST = '0.0.0.0';

const options = {
  hostname: HOST,
  port: PORT,
  path: '/api/health',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const result = JSON.parse(data);
      if (result.status === 'healthy') {
        console.log('✅ Health check passed');
        process.exit(0);
      } else {
        console.error('❌ Health check failed: Invalid status');
        process.exit(1);
      }
    } catch (error) {
      console.error('❌ Health check failed: Invalid JSON response');
      process.exit(1);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Health check failed:', error.message);
  process.exit(1);
});

req.on('timeout', () => {
  console.error('❌ Health check failed: Timeout');
  req.destroy();
  process.exit(1);
});

req.end();
