// Simple deployment test script
const http = require('http');

const PORT = process.env.PORT || 3001;
const HOST = 'localhost';

// Test health endpoint
function testHealth() {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: HOST,
      port: PORT,
      path: '/api/health',
      method: 'GET'
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('✅ Health check passed:', result.status);
          resolve(result);
        } catch (e) {
          reject(e);
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(5000, () => reject(new Error('Timeout')));
    req.end();
  });
}

// Test search endpoint
function testSearch() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ query: 'Eiffel Tower' });
    
    const req = http.request({
      hostname: HOST,
      port: PORT,
      path: '/api/search',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('✅ Search test passed:', result.zip);
          resolve(result);
        } catch (e) {
          reject(e);
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(5000, () => reject(new Error('Timeout')));
    req.write(postData);
    req.end();
  });
}

// Run tests
async function runTests() {
  console.log('🧪 Testing deployment...');
  
  try {
    await testHealth();
    await testSearch();
    console.log('🎉 All tests passed! Deployment is working correctly.');
    process.exit(0);
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Wait a bit for server to start, then run tests
setTimeout(runTests, 2000);
