{"name": "zipcode-finder-backend", "version": "1.0.0", "description": "Backend API for ZipCode Finder application", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "postinstall": "npm run build", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["zipcode", "geocoding", "api", "express", "typescript"], "author": "ZipCode Finder Team", "license": "MIT", "dependencies": {"express": "^4.19.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.4.1", "axios": "^1.7.7", "dotenv": "^16.4.5", "zod": "^3.25.76", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^22.16.5", "typescript": "^5.8.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "jest": "^29.7.0", "@types/jest": "^29.5.14", "ts-jest": "^29.2.5", "tsx": "^4.19.2"}, "engines": {"node": ">=18.0.0"}}