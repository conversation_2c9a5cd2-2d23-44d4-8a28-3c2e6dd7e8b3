"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const environment_1 = require("./config/environment");
const api_1 = __importDefault(require("./routes/api"));
const security_1 = require("./middleware/security");
const app = (0, express_1.default)();
app.use(security_1.securityMiddleware);
app.use(security_1.corsMiddleware);
app.use(security_1.rateLimitMiddleware);
app.use(express_1.default.json({ limit: "10mb" }));
app.use(express_1.default.urlencoded({ extended: true, limit: "10mb" }));
if (environment_1.config.isDevelopment) {
    app.use(security_1.loggingMiddleware);
}
app.use("/api", api_1.default);
app.get("/", (_req, res) => {
    res.json({
        name: "ZipCode Finder API",
        version: "1.0.0",
        description: "Backend API for finding ZIP codes and location information",
        endpoints: {
            search: "POST /api/search",
            health: "GET /api/health",
        },
        documentation: "https://github.com/your-username/zipcode-finder-backend",
    });
});
app.use("*", (_req, res) => {
    res.status(404).json({
        error: "bad_request",
        message: "Endpoint not found",
    });
});
app.use(security_1.errorHandler);
const server = app.listen(environment_1.config.port, () => {
    console.log(`🚀 ZipCode Finder API running on port ${environment_1.config.port}`);
    console.log(`📍 Environment: ${environment_1.config.nodeEnv}`);
    console.log(`🔑 Google Maps API: ${environment_1.config.googleMapsApiKey ? "Configured" : "Not configured"}`);
    console.log(`🔑 OpenCage API: ${environment_1.config.openCageApiKey ? "Configured" : "Not configured"}`);
    console.log(`🌐 CORS Origins: ${environment_1.config.allowedOrigins.join(", ")}`);
});
process.on("SIGTERM", () => {
    console.log("SIGTERM received, shutting down gracefully");
    server.close(() => {
        console.log("Process terminated");
        process.exit(0);
    });
});
process.on("SIGINT", () => {
    console.log("SIGINT received, shutting down gracefully");
    server.close(() => {
        console.log("Process terminated");
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=server.js.map