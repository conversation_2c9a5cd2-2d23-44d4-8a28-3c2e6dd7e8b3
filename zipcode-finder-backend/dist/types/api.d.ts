export interface CountryInfo {
    name: string;
    iso2: string;
}
export interface SearchResponse {
    zip: string;
    place_name: string;
    lat: number;
    lng: number;
    state: string;
    country: CountryInfo;
    continent: string;
    image_url: string;
    google_maps_url: string;
}
export interface ApiError {
    error: "zipcode_not_found" | "bad_request" | "server_error";
    message: string;
}
export interface SearchRequest {
    query: string;
}
export interface GeocodingResult {
    lat: number;
    lng: number;
    formatted_address: string;
    address_components: AddressComponent[];
    place_id?: string;
}
export interface AddressComponent {
    long_name: string;
    short_name: string;
    types: string[];
}
export interface GoogleGeocodingResponse {
    results: {
        formatted_address: string;
        geometry: {
            location: {
                lat: number;
                lng: number;
            };
        };
        address_components: AddressComponent[];
        place_id: string;
    }[];
    status: string;
}
export interface OpenCageResponse {
    results: {
        formatted: string;
        geometry: {
            lat: number;
            lng: number;
        };
        components: {
            [key: string]: string;
        };
    }[];
    status: {
        code: number;
        message: string;
    };
}
//# sourceMappingURL=api.d.ts.map