import { Request, Response, NextFunction } from "express";
import cors from "cors";
export declare const corsMiddleware: (req: cors.CorsRequest, res: {
    statusCode?: number | undefined;
    setHeader(key: string, value: string): any;
    end(): any;
}, next: (err?: any) => any) => void;
export declare const rateLimitMiddleware: import("express-rate-limit").RateLimitRequestHandler;
export declare const securityMiddleware: (req: import("http").IncomingMessage, res: import("http").ServerResponse, next: (err?: unknown) => void) => void;
export declare const loggingMiddleware: (req: Request, res: Response, next: NextFunction) => void;
export declare const errorHandler: (error: any, _req: Request, res: Response, _next: NextFunction) => void;
//# sourceMappingURL=security.d.ts.map