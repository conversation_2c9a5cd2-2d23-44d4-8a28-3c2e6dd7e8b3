"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.loggingMiddleware = exports.securityMiddleware = exports.rateLimitMiddleware = exports.corsMiddleware = void 0;
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const helmet_1 = __importDefault(require("helmet"));
const cors_1 = __importDefault(require("cors"));
const environment_1 = require("../config/environment");
exports.corsMiddleware = (0, cors_1.default)({
    origin: (origin, callback) => {
        if (!origin)
            return callback(null, true);
        if (environment_1.config.allowedOrigins.includes(origin)) {
            return callback(null, true);
        }
        if (environment_1.config.isDevelopment && origin.includes("localhost")) {
            return callback(null, true);
        }
        callback(new Error("Not allowed by CORS"));
    },
    credentials: true,
    methods: ["GET", "POST", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
});
exports.rateLimitMiddleware = (0, express_rate_limit_1.default)({
    windowMs: environment_1.config.rateLimit.windowMs,
    max: environment_1.config.rateLimit.maxRequests,
    message: {
        error: "server_error",
        message: "Too many requests from this IP, please try again later.",
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        return req.path === "/api/health";
    },
});
exports.securityMiddleware = (0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: [
                "'self'",
                "https://maps.googleapis.com",
                "https://api.opencagedata.com",
            ],
        },
    },
    crossOriginEmbedderPolicy: false,
});
const loggingMiddleware = (req, res, next) => {
    const start = Date.now();
    res.on("finish", () => {
        const duration = Date.now() - start;
        const { method, url, ip } = req;
        const { statusCode } = res;
        console.log(`${method} ${url} - ${statusCode} - ${duration}ms - ${ip}`);
    });
    next();
};
exports.loggingMiddleware = loggingMiddleware;
const errorHandler = (error, _req, res, _next) => {
    console.error("Unhandled error:", error);
    if (error.message === "Not allowed by CORS") {
        res.status(403).json({
            error: "server_error",
            message: "CORS policy violation",
        });
        return;
    }
    res.status(500).json({
        error: "server_error",
        message: "Internal server error",
    });
};
exports.errorHandler = errorHandler;
//# sourceMappingURL=security.js.map