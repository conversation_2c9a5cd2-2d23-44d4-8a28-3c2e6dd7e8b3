{"version": 3, "file": "demo.js", "sourceRoot": "", "sources": ["../../src/services/demo.ts"], "names": [], "mappings": ";;;AACA,oDAAuD;AAEvD,SAAS,eAAe,CAAC,GAAW,EAAE,GAAW;IAC/C,OAAO,mDAAmD,GAAG,IAAI,GAAG,EAAE,CAAC;AACzE,CAAC;AAED,MAAa,WAAW;IACL,QAAQ,GAAmC;QAC1D,cAAc,EAAE;YACd,GAAG,EAAE,OAAO;YACZ,UAAU,EAAE,qBAAqB;YACjC,GAAG,EAAE,OAAO;YACZ,GAAG,EAAE,MAAM;YACX,KAAK,EAAE,eAAe;YACtB,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE;YACvC,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,kBAAkB;YAC7B,eAAe,EAAE,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;SAClD;QACD,uCAAuC,EAAE;YACvC,GAAG,EAAE,OAAO;YACZ,UAAU,EAAE,2BAA2B;YACvC,GAAG,EAAE,MAAM;YACX,GAAG,EAAE,CAAC,OAAO;YACb,KAAK,EAAE,YAAY;YACnB,OAAO,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE;YAC9C,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,kBAAkB;YAC7B,eAAe,EAAE,eAAe,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC;SACnD;QACD,gBAAgB,EAAE;YAChB,GAAG,EAAE,OAAO;YACZ,UAAU,EAAE,oBAAoB;YAChC,GAAG,EAAE,OAAO;YACZ,GAAG,EAAE,CAAC,OAAO;YACb,KAAK,EAAE,UAAU;YACjB,OAAO,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE;YAC9C,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,kBAAkB;YAC7B,eAAe,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;SACpD;QACD,QAAQ,EAAE;YACR,GAAG,EAAE,UAAU;YACf,UAAU,EAAE,qBAAqB;YACjC,GAAG,EAAE,OAAO;YACZ,GAAG,EAAE,CAAC,MAAM;YACZ,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE;YAC/C,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,kBAAkB;YAC7B,eAAe,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;SACnD;QACD,OAAO,EAAE;YACP,GAAG,EAAE,UAAU;YACf,UAAU,EAAE,gBAAgB;YAC5B,GAAG,EAAE,OAAO;YACZ,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,OAAO;YACd,OAAO,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;YACtC,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,kBAAkB;YAC7B,eAAe,EAAE,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;SACpD;QACD,QAAQ,EAAE;YACR,GAAG,EAAE,MAAM;YACX,UAAU,EAAE,aAAa;YACzB,GAAG,EAAE,CAAC,OAAO;YACb,GAAG,EAAE,QAAQ;YACb,KAAK,EAAE,iBAAiB;YACxB,OAAO,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE;YAC1C,SAAS,EAAE,WAAW;YACtB,SAAS,EAAE,kBAAkB;YAC7B,eAAe,EAAE,eAAe,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC;SACrD;KACF,CAAC;IAEF,cAAc,CAAC,KAAa;QAC1B,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAGnD,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC;QAGD,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,KAAK,CAAC,CAAC;QACvC,IAAI,MAAM,EAAE,CAAC;YACX,OAAO;gBACL,GAAG,EAAE,OAAO;gBACZ,UAAU,EAAE,kBAAkB,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG;gBAChF,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE;gBACtC,SAAS,EAAE,SAAS;gBACpB,SAAS,EAAE,kBAAkB;gBAC7B,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC;aACzD,CAAC;QACJ,CAAC;QAGD,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzD,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBACnE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAGD,MAAM,KAAK,GAAQ,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC1E,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;QACnB,KAAK,CAAC,IAAI,GAAG,mBAAmB,CAAC;QACjC,MAAM,KAAK,CAAC;IACd,CAAC;CACF;AA3GD,kCA2GC"}