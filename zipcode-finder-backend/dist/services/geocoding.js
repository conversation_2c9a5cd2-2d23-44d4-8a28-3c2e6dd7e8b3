"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeocodingService = void 0;
const axios_1 = __importDefault(require("axios"));
const environment_1 = require("../config/environment");
const validation_1 = require("../utils/validation");
const CONTINENT_MAP = {
    US: "North America",
    CA: "North America",
    MX: "North America",
    GB: "Europe",
    FR: "Europe",
    DE: "Europe",
    IT: "Europe",
    ES: "Europe",
    AU: "Australia",
    NZ: "Australia",
    JP: "Asia",
    CN: "Asia",
    IN: "Asia",
    KR: "Asia",
    BR: "South America",
    AR: "South America",
    CL: "South America",
    ZA: "Africa",
    EG: "Africa",
    NG: "Africa",
};
class GeocodingService {
    async geocodeWithGoogle(query) {
        if (!environment_1.config.googleMapsApiKey)
            return null;
        try {
            const response = await axios_1.default.get("https://maps.googleapis.com/maps/api/geocode/json", {
                params: {
                    address: query,
                    key: environment_1.config.googleMapsApiKey,
                },
                timeout: 10000,
            });
            if (response.data.status !== "OK" || !response.data.results.length) {
                return null;
            }
            const result = response.data.results[0];
            if (!result)
                return null;
            return {
                lat: result.geometry.location.lat,
                lng: result.geometry.location.lng,
                formatted_address: result.formatted_address,
                address_components: result.address_components,
                place_id: result.place_id,
            };
        }
        catch (error) {
            console.error("Google Geocoding API error:", error);
            return null;
        }
    }
    async geocodeWithOpenCage(query) {
        if (!environment_1.config.openCageApiKey)
            return null;
        try {
            const response = await axios_1.default.get("https://api.opencagedata.com/geocode/v1/json", {
                params: {
                    q: query,
                    key: environment_1.config.openCageApiKey,
                    limit: 1,
                    no_annotations: 1,
                },
                timeout: 10000,
            });
            if (response.data.status.code !== 200 || !response.data.results.length) {
                return null;
            }
            const result = response.data.results[0];
            if (!result)
                return null;
            const addressComponents = Object.entries(result.components).map(([type, value]) => ({
                long_name: value,
                short_name: value,
                types: [type],
            }));
            return {
                lat: result.geometry.lat,
                lng: result.geometry.lng,
                formatted_address: result.formatted,
                address_components: addressComponents,
            };
        }
        catch (error) {
            console.error("OpenCage Geocoding API error:", error);
            return null;
        }
    }
    extractLocationInfo(geocodingResult) {
        const components = geocodingResult.address_components;
        let zip = "";
        let state = "";
        let country = { name: "Unknown", iso2: "" };
        for (const component of components) {
            if (component.types.includes("postal_code")) {
                zip = component.long_name;
            }
            else if (component.types.includes("administrative_area_level_1")) {
                state = component.long_name;
            }
            else if (component.types.includes("country")) {
                country = {
                    name: component.long_name,
                    iso2: component.short_name,
                };
            }
        }
        return {
            zip: zip || "00000",
            state: state || "",
            country,
            place_name: geocodingResult.formatted_address,
        };
    }
    generateMapsUrl(lat, lng) {
        return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
    }
    generateStaticMapUrl(lat, lng) {
        if (!environment_1.config.googleMapsApiKey) {
            return "/placeholder.svg";
        }
        return `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=13&size=400x300&markers=color:red%7C${lat},${lng}&key=${environment_1.config.googleMapsApiKey}`;
    }
    async searchLocation(query) {
        const coords = (0, validation_1.parseCoordinates)(query);
        if (coords) {
            return {
                zip: "00000",
                place_name: `Location (${coords.lat.toFixed(4)}, ${coords.lng.toFixed(4)})`,
                lat: coords.lat,
                lng: coords.lng,
                state: "",
                country: { name: "Unknown", iso2: "" },
                continent: "Unknown",
                image_url: this.generateStaticMapUrl(coords.lat, coords.lng),
                google_maps_url: this.generateMapsUrl(coords.lat, coords.lng),
            };
        }
        let geocodingResult = null;
        geocodingResult = await this.geocodeWithGoogle(query);
        if (!geocodingResult) {
            geocodingResult = await this.geocodeWithOpenCage(query);
        }
        if (!geocodingResult) {
            const error = new Error("Location not found");
            error.status = 404;
            error.code = "zipcode_not_found";
            throw error;
        }
        const locationInfo = this.extractLocationInfo(geocodingResult);
        const continent = CONTINENT_MAP[locationInfo.country.iso2] || "Unknown";
        return {
            zip: locationInfo.zip,
            place_name: locationInfo.place_name,
            lat: geocodingResult.lat,
            lng: geocodingResult.lng,
            state: locationInfo.state,
            country: locationInfo.country,
            continent,
            image_url: this.generateStaticMapUrl(geocodingResult.lat, geocodingResult.lng),
            google_maps_url: this.generateMapsUrl(geocodingResult.lat, geocodingResult.lng),
        };
    }
}
exports.GeocodingService = GeocodingService;
//# sourceMappingURL=geocoding.js.map