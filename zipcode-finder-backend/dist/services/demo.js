"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DemoService = void 0;
const validation_1 = require("../utils/validation");
function generateMapsUrl(lat, lng) {
    return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
}
class DemoService {
    demoData = {
        'eiffel tower': {
            zip: '75007',
            place_name: 'Eiffel Tower, Paris',
            lat: 48.8584,
            lng: 2.2945,
            state: 'Île-de-France',
            country: { name: 'France', iso2: 'FR' },
            continent: 'Europe',
            image_url: '/placeholder.svg',
            google_maps_url: generateMapsUrl(48.8584, 2.2945),
        },
        '1600 amphitheatre pkwy, mountain view': {
            zip: '94043',
            place_name: 'Googleplex, Mountain View',
            lat: 37.422,
            lng: -122.084,
            state: 'California',
            country: { name: 'United States', iso2: 'US' },
            continent: 'North America',
            image_url: '/placeholder.svg',
            google_maps_url: generateMapsUrl(37.422, -122.084),
        },
        'new york 10001': {
            zip: '10001',
            place_name: 'New York, NY 10001',
            lat: 40.7506,
            lng: -73.9972,
            state: 'New York',
            country: { name: 'United States', iso2: 'US' },
            continent: 'North America',
            image_url: '/placeholder.svg',
            google_maps_url: generateMapsUrl(40.7506, -73.9972),
        },
        'london': {
            zip: 'SW1A 1AA',
            place_name: 'Westminster, London',
            lat: 51.5014,
            lng: -0.1419,
            state: 'England',
            country: { name: 'United Kingdom', iso2: 'GB' },
            continent: 'Europe',
            image_url: '/placeholder.svg',
            google_maps_url: generateMapsUrl(51.5014, -0.1419),
        },
        'tokyo': {
            zip: '100-0001',
            place_name: 'Chiyoda, Tokyo',
            lat: 35.6762,
            lng: 139.6503,
            state: 'Tokyo',
            country: { name: 'Japan', iso2: 'JP' },
            continent: 'Asia',
            image_url: '/placeholder.svg',
            google_maps_url: generateMapsUrl(35.6762, 139.6503),
        },
        'sydney': {
            zip: '2000',
            place_name: 'Sydney, NSW',
            lat: -33.8688,
            lng: 151.2093,
            state: 'New South Wales',
            country: { name: 'Australia', iso2: 'AU' },
            continent: 'Australia',
            image_url: '/placeholder.svg',
            google_maps_url: generateMapsUrl(-33.8688, 151.2093),
        },
    };
    searchLocation(query) {
        const normalizedQuery = query.trim().toLowerCase();
        if (this.demoData[normalizedQuery]) {
            return this.demoData[normalizedQuery];
        }
        const coords = (0, validation_1.parseCoordinates)(query);
        if (coords) {
            return {
                zip: '00000',
                place_name: `Demo Location (${coords.lat.toFixed(4)}, ${coords.lng.toFixed(4)})`,
                lat: coords.lat,
                lng: coords.lng,
                state: '',
                country: { name: 'Unknown', iso2: '' },
                continent: 'Unknown',
                image_url: '/placeholder.svg',
                google_maps_url: generateMapsUrl(coords.lat, coords.lng),
            };
        }
        for (const [key, value] of Object.entries(this.demoData)) {
            if (key.includes(normalizedQuery) || normalizedQuery.includes(key)) {
                return value;
            }
        }
        const error = new Error('Postal code not found for the given input');
        error.status = 404;
        error.code = 'zipcode_not_found';
        throw error;
    }
}
exports.DemoService = DemoService;
//# sourceMappingURL=demo.js.map