import { z } from "zod";
export declare const searchRequestSchema: z.ZodObject<{
    query: z.ZodString;
}, "strip", z.ZodTypeAny, {
    query: string;
}, {
    query: string;
}>;
export declare function isLatLng(input: string): boolean;
export declare function isZipCode(input: string): boolean;
export declare function parseCoordinates(input: string): {
    lat: number;
    lng: number;
} | null;
export declare function sanitizeQuery(query: string): string;
//# sourceMappingURL=validation.d.ts.map