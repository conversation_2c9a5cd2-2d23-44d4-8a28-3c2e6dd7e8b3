"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchRequestSchema = void 0;
exports.isLatLng = isLatLng;
exports.isZipCode = isZipCode;
exports.parseCoordinates = parseCoordinates;
exports.sanitizeQuery = sanitizeQuery;
const zod_1 = require("zod");
exports.searchRequestSchema = zod_1.z.object({
    query: zod_1.z
        .string()
        .min(1, "Query cannot be empty")
        .max(500, "Query too long")
        .trim(),
});
function isLatLng(input) {
    return /^-?\d{1,3}(\.\d+)?,\s*-?\d{1,3}(\.\d+)?$/.test(input.trim());
}
function isZipCode(input) {
    const usZip = /^\d{5}(-\d{4})?$/;
    const ukPostal = /^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i;
    const caPostal = /^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/i;
    const trimmed = input.trim();
    return (usZip.test(trimmed) || ukPostal.test(trimmed) || caPostal.test(trimmed));
}
function parseCoordinates(input) {
    if (!isLatLng(input))
        return null;
    const [latStr, lngStr] = input.split(",").map((s) => s.trim());
    if (!latStr || !lngStr)
        return null;
    const lat = parseFloat(latStr);
    const lng = parseFloat(lngStr);
    if (isNaN(lat) || isNaN(lng))
        return null;
    if (lat < -90 || lat > 90)
        return null;
    if (lng < -180 || lng > 180)
        return null;
    return { lat, lng };
}
function sanitizeQuery(query) {
    return query
        .trim()
        .replace(/[<>]/g, "")
        .substring(0, 500);
}
//# sourceMappingURL=validation.js.map