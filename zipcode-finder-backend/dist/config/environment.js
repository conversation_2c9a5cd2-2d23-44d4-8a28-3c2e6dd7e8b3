"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
dotenv_1.default.config();
const envSchema = zod_1.z.object({
    PORT: zod_1.z.string().default('3001').transform(Number),
    NODE_ENV: zod_1.z.enum(['development', 'production', 'test']).default('development'),
    GOOGLE_MAPS_API_KEY: zod_1.z.string().optional(),
    OPENCAGE_API_KEY: zod_1.z.string().optional(),
    ALLOWED_ORIGINS: zod_1.z.string().default('http://localhost:5173'),
    RATE_LIMIT_WINDOW_MS: zod_1.z.string().default('900000').transform(Number),
    RATE_LIMIT_MAX_REQUESTS: zod_1.z.string().default('100').transform(Number),
});
const env = envSchema.parse(process.env);
if (!env.GOOGLE_MAPS_API_KEY && !env.OPENCAGE_API_KEY) {
    console.warn('Warning: No geocoding API keys provided. The service will use demo data only.');
}
exports.config = {
    port: env.PORT,
    nodeEnv: env.NODE_ENV,
    googleMapsApiKey: env.GOOGLE_MAPS_API_KEY,
    openCageApiKey: env.OPENCAGE_API_KEY,
    allowedOrigins: env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()),
    rateLimit: {
        windowMs: env.RATE_LIMIT_WINDOW_MS,
        maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
    },
    isDevelopment: env.NODE_ENV === 'development',
    isProduction: env.NODE_ENV === 'production',
};
//# sourceMappingURL=environment.js.map