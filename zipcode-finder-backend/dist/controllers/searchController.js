"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchController = void 0;
const geocoding_1 = require("../services/geocoding");
const demo_1 = require("../services/demo");
const validation_1 = require("../utils/validation");
const environment_1 = require("../config/environment");
class SearchController {
    geocodingService;
    demoService;
    constructor() {
        this.geocodingService = new geocoding_1.GeocodingService();
        this.demoService = new demo_1.DemoService();
    }
    search = async (req, res) => {
        try {
            const validation = validation_1.searchRequestSchema.safeParse(req.body);
            if (!validation.success) {
                const error = {
                    error: "bad_request",
                    message: validation.error.errors[0]?.message || "Invalid request",
                };
                res.status(400).json(error);
                return;
            }
            const { query } = validation.data;
            let result;
            try {
                if (environment_1.config.googleMapsApiKey || environment_1.config.openCageApiKey) {
                    result = await this.geocodingService.searchLocation(query);
                }
                else {
                    result = this.demoService.searchLocation(query);
                }
            }
            catch (geocodingError) {
                try {
                    result = this.demoService.searchLocation(query);
                }
                catch (demoError) {
                    const error = {
                        error: demoError.code === "zipcode_not_found"
                            ? "zipcode_not_found"
                            : "server_error",
                        message: demoError.message || "Location not found",
                    };
                    res.status(demoError.status || 404).json(error);
                    return;
                }
            }
            res.json(result);
        }
        catch (error) {
            console.error("Search controller error:", error);
            const apiError = {
                error: "server_error",
                message: "An unexpected error occurred",
            };
            res.status(500).json(apiError);
        }
    };
    health = (_req, res) => {
        const hasApiKeys = !!(environment_1.config.googleMapsApiKey || environment_1.config.openCageApiKey);
        res.json({
            status: "healthy",
            timestamp: new Date().toISOString(),
            version: "1.0.0",
            services: {
                geocoding: hasApiKeys ? "available" : "demo_mode",
                google_maps: !!environment_1.config.googleMapsApiKey,
                opencage: !!environment_1.config.openCageApiKey,
            },
        });
    };
}
exports.SearchController = SearchController;
//# sourceMappingURL=searchController.js.map