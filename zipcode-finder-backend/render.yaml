services:
  - type: web
    name: zipcode-finder-backend
    runtime: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: ALLOWED_ORIGINS
        value: https://myzipcodefinder.onrender.com,http://localhost:5173
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX_REQUESTS
        value: 100
      # These should be set in Render dashboard as secrets:
      # - GOOGLE_MAPS_API_KEY
      # - OPENCAGE_API_KEY
