import { z } from "zod";

// Request validation schemas
export const searchRequestSchema = z.object({
  query: z
    .string()
    .min(1, "Query cannot be empty")
    .max(500, "Query too long")
    .trim(),
});

// Utility functions for input validation
export function isLatLng(input: string): boolean {
  return /^-?\d{1,3}(\.\d+)?,\s*-?\d{1,3}(\.\d+)?$/.test(input.trim());
}

export function isZipCode(input: string): boolean {
  // US ZIP codes (5 digits or 5+4 format)
  const usZip = /^\d{5}(-\d{4})?$/;
  // UK postal codes
  const ukPostal = /^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i;
  // Canadian postal codes
  const caPostal = /^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/i;

  const trimmed = input.trim();
  return (
    usZip.test(trimmed) || ukPostal.test(trimmed) || caPostal.test(trimmed)
  );
}

export function parseCoordinates(
  input: string
): { lat: number; lng: number } | null {
  if (!isLatLng(input)) return null;

  const [latStr, lngStr] = input.split(",").map((s) => s.trim());
  if (!latStr || !lngStr) return null;

  const lat = parseFloat(latStr);
  const lng = parseFloat(lngStr);

  if (isNaN(lat) || isNaN(lng)) return null;
  if (lat < -90 || lat > 90) return null;
  if (lng < -180 || lng > 180) return null;

  return { lat, lng };
}

export function sanitizeQuery(query: string): string {
  return query
    .trim()
    .replace(/[<>]/g, "") // Remove potential HTML tags
    .substring(0, 500); // Limit length
}
