import express from "express";
import { config } from "./config/environment";

// Log startup information
console.log("🔧 Starting ZipCode Finder API...");
console.log(`📍 Node Environment: ${process.env.NODE_ENV || "development"}`);
console.log(`🚪 Port: ${process.env.PORT || config.port}`);
console.log(`🌐 Host: ${config.isProduction ? "0.0.0.0" : "localhost"}`);
import apiRoutes from "./routes/api";
import {
  corsMiddleware,
  rateLimitMiddleware,
  securityMiddleware,
  loggingMiddleware,
  errorHandler,
} from "./middleware/security";

const app = express();

// Security middleware
app.use(securityMiddleware);
app.use(corsMiddleware);
app.use(rateLimitMiddleware);

// Request parsing
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Logging
if (config.isDevelopment) {
  app.use(loggingMiddleware);
}

// API routes
app.use("/api", apiRoutes);

// Root endpoint
app.get("/", (_req, res) => {
  res.json({
    name: "ZipCode Finder API",
    version: "1.0.0",
    description: "Backend API for finding ZIP codes and location information",
    endpoints: {
      search: "POST /api/search",
      health: "GET /api/health",
    },
    documentation: "https://github.com/your-username/zipcode-finder-backend",
  });
});

// 404 handler
app.use("*", (_req, res) => {
  res.status(404).json({
    error: "bad_request",
    message: "Endpoint not found",
  });
});

// Error handler (must be last)
app.use(errorHandler);

// Start server
const HOST = config.isProduction ? "0.0.0.0" : "localhost";
const server = app.listen(config.port, HOST, () => {
  console.log(`🚀 ZipCode Finder API running on ${HOST}:${config.port}`);
  console.log(`📍 Environment: ${config.nodeEnv}`);
  console.log(
    `🔑 Google Maps API: ${
      config.googleMapsApiKey ? "Configured" : "Not configured"
    }`
  );
  console.log(
    `🔑 OpenCage API: ${
      config.openCageApiKey ? "Configured" : "Not configured"
    }`
  );
  console.log(`🌐 CORS Origins: ${config.allowedOrigins.join(", ")}`);
});

// Handle server startup errors
server.on("error", (error: any) => {
  if (error.code === "EADDRINUSE") {
    console.error(`❌ Port ${config.port} is already in use`);
    process.exit(1);
  } else {
    console.error("❌ Server startup error:", error);
    process.exit(1);
  }
});

// Graceful shutdown
process.on("SIGTERM", () => {
  console.log("SIGTERM received, shutting down gracefully");
  server.close(() => {
    console.log("Process terminated");
    process.exit(0);
  });
});

process.on("SIGINT", () => {
  console.log("SIGINT received, shutting down gracefully");
  server.close(() => {
    console.log("Process terminated");
    process.exit(0);
  });
});

export default app;
