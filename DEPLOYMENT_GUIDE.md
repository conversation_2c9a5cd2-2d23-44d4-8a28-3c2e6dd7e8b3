# ZipCode Finder - Complete Deployment Guide

This guide will help you deploy both the frontend and backend of the ZipCode Finder application.

## 🏗️ Architecture Overview

- **Frontend**: React/TypeScript application (Vite)
- **Backend**: Node.js/Express API with TypeScript
- **Deployment**: Render.com for both frontend and backend
- **APIs**: Google Maps Geocoding API, Google Maps Static API

## 📋 Prerequisites

1. **GitHub Account**: Both repositories should be on GitHub
2. **Render Account**: Sign up at [render.com](https://render.com)
3. **Google Cloud Account**: For Google Maps API keys (optional, demo mode available)

## 🔧 Backend Deployment (Step 1)

### 1. Prepare the Backend Repository

Ensure your `zipcode-finder-backend` repository contains:
- All source files in `src/` directory
- `package.json` with correct scripts
- `render.yaml` configuration file
- `Dockerfile` (optional, for Docker deployment)

### 2. Deploy to Render

1. **Connect Repository**:
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New +" → "Web Service"
   - Connect your GitHub account
   - Select the `zipcode-finder-backend` repository

2. **Configure Service**:
   - **Name**: `zipcode-finder-backend`
   - **Environment**: `Node`
   - **Build Command**: `npm install && npm run build`
   - **Start Command**: `npm start`
   - **Plan**: Free (or paid for better performance)

3. **Set Environment Variables**:
   ```
   NODE_ENV=production
   PORT=10000
   ALLOWED_ORIGINS=https://your-frontend-domain.onrender.com,http://localhost:5173
   GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
   OPENCAGE_API_KEY=your_opencage_api_key_here (optional)
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   ```

4. **Deploy**:
   - Click "Create Web Service"
   - Wait for deployment to complete
   - Note the backend URL (e.g., `https://zipcode-finder-backend.onrender.com`)

### 3. Test Backend Deployment

```bash
# Test health endpoint
curl https://your-backend-url.onrender.com/api/health

# Test search endpoint
curl -X POST https://your-backend-url.onrender.com/api/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Eiffel Tower"}'
```

## 🎨 Frontend Deployment (Step 2)

### 1. Update Frontend Configuration

1. **Update API URL**:
   - Edit `zipcode-finder/.env` or create it:
   ```env
   VITE_API_BASE_URL=https://your-backend-url.onrender.com
   ```

2. **Update Production Environment**:
   - For production deployment, the environment variable will be set in Render

### 2. Deploy to Render

1. **Connect Repository**:
   - Go to [Render Dashboard](https://dashboard.render.com)
   - Click "New +" → "Static Site"
   - Select the `zipcode-finder` repository

2. **Configure Static Site**:
   - **Name**: `zipcode-finder` or `myzipcodefinder`
   - **Build Command**: `npm install && npm run build`
   - **Publish Directory**: `dist`

3. **Set Environment Variables**:
   ```
   VITE_API_BASE_URL=https://your-backend-url.onrender.com
   ```

4. **Deploy**:
   - Click "Create Static Site"
   - Wait for deployment to complete
   - Your site will be available at `https://myzipcodefinder.onrender.com`

### 3. Update Backend CORS

After frontend deployment, update the backend's `ALLOWED_ORIGINS` environment variable:
```
ALLOWED_ORIGINS=https://myzipcodefinder.onrender.com,http://localhost:5173
```

## 🔑 API Keys Setup (Optional but Recommended)

### Google Maps API Setup

1. **Create Google Cloud Project**:
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Create a new project or select existing one

2. **Enable APIs**:
   - Enable "Geocoding API"
   - Enable "Maps Static API"

3. **Create API Key**:
   - Go to "Credentials" → "Create Credentials" → "API Key"
   - Restrict the key to your domain and required APIs
   - Copy the API key

4. **Set in Render**:
   - Go to your backend service in Render
   - Add environment variable: `GOOGLE_MAPS_API_KEY=your_api_key_here`

### OpenCage API Setup (Alternative)

1. **Sign up**: Go to [OpenCage](https://opencagedata.com)
2. **Get API Key**: Free tier available (2,500 requests/day)
3. **Set in Render**: `OPENCAGE_API_KEY=your_api_key_here`

## 🧪 Testing the Deployment

### 1. Backend Tests

```bash
# Health check
curl https://your-backend-url.onrender.com/api/health

# Search by address
curl -X POST https://your-backend-url.onrender.com/api/search \
  -H "Content-Type: application/json" \
  -d '{"query": "1600 Amphitheatre Pkwy, Mountain View, CA"}'

# Search by coordinates
curl -X POST https://your-backend-url.onrender.com/api/search \
  -H "Content-Type: application/json" \
  -d '{"query": "37.422,-122.084"}'

# Search by place name
curl -X POST https://your-backend-url.onrender.com/api/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Eiffel Tower"}'
```

### 2. Frontend Tests

1. **Open the website**: `https://myzipcodefinder.onrender.com`
2. **Test searches**:
   - Try "Eiffel Tower"
   - Try "1600 Amphitheatre Pkwy, Mountain View"
   - Try coordinates "37.422,-122.084"
   - Try "New York 10001"

### 3. Integration Tests

1. **Check Network Tab**: Verify API calls are going to the correct backend URL
2. **Test Error Handling**: Try invalid queries to ensure proper error messages
3. **Test Fallback**: If API keys aren't working, ensure demo data is returned

## 🔧 Troubleshooting

### Common Issues

1. **CORS Errors**:
   - Ensure `ALLOWED_ORIGINS` includes your frontend domain
   - Check that both HTTP and HTTPS are handled correctly

2. **API Key Issues**:
   - Verify API keys are correctly set in Render environment variables
   - Check Google Cloud Console for API usage and restrictions
   - Ensure APIs are enabled in Google Cloud Console

3. **Build Failures**:
   - Check build logs in Render dashboard
   - Ensure all dependencies are in `package.json`
   - Verify Node.js version compatibility

4. **Backend Not Responding**:
   - Check backend logs in Render dashboard
   - Verify environment variables are set correctly
   - Test health endpoint first

### Performance Optimization

1. **Backend**:
   - Consider upgrading to paid Render plan for better performance
   - Implement caching for frequently requested locations
   - Add request logging and monitoring

2. **Frontend**:
   - Enable gzip compression
   - Optimize images and assets
   - Consider CDN for static assets

## 📊 Monitoring

### Health Checks

- Backend: `GET /api/health`
- Monitor response times and error rates
- Set up alerts for service downtime

### Analytics

- Track popular search queries
- Monitor API usage and costs
- Set up error tracking (e.g., Sentry)

## 🚀 Going Live

1. **Custom Domain** (Optional):
   - Configure custom domain in Render
   - Update CORS settings accordingly

2. **SSL Certificate**:
   - Render provides free SSL certificates
   - Ensure HTTPS is enforced

3. **Final Testing**:
   - Test all functionality on production
   - Verify mobile responsiveness
   - Check performance metrics

## 📝 Maintenance

### Regular Tasks

1. **Update Dependencies**: Keep packages up to date
2. **Monitor API Usage**: Track Google Maps API costs
3. **Review Logs**: Check for errors and performance issues
4. **Backup**: Ensure code is backed up in version control

### Scaling

- Monitor usage and upgrade Render plans as needed
- Consider implementing rate limiting per user
- Add database for caching popular queries

---

## 🎉 Congratulations!

Your ZipCode Finder application is now live at:
- **Frontend**: `https://myzipcodefinder.onrender.com`
- **Backend**: `https://your-backend-url.onrender.com`

The application provides a robust ZIP code lookup service with fallback to demo data when API services are unavailable.
