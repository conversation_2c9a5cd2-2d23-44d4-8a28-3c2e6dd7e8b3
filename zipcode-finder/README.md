# ZipFinder

ZipFinder is a privacy-friendly tool to find the postal/ZIP code for any location input (address, place name, or coordinates). It returns the ZIP, place name, latitude/longitude, state, country (name + ISO2), continent, an image, and a link to Google Maps.

Key features
- Accurate data via Google Geocoding + Static Maps; optional fallbacks (GeoNames, Positionstack)
- Secure backend: Supabase Edge Function (no API keys in the frontend)
- Strict error handling with standardized JSON errors
- Responsive, accessible UI (React + Vite + Tailwind + shadcn)
- Caching layer to reduce API calls
- Tests + CI (Vitest for frontend, Deno tests for functions)
- Dockerfile + docker-compose for the frontend

Architecture (high level)
- Frontend (Vite React app): calls Supabase Edge Function using the Supabase JS client
- Edge Function zipsearch: orchestrates geocoding and fallbacks, returns complete result or 404
- Image: generated via Google Static Maps on the backend and returned as a data URL (no key leakage)

API
POST (Edge Function): zipsearch
Request: { "query": string }
Success 200:
{
  "zip":"94043",
  "place_name":"1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA",
  "lat":37.422,
  "lng":-122.084,
  "state":"California",
  "country":{"name":"United States","iso2":"US"},
  "continent":"North America",
  "image_url":"data:image/png;base64,...",
  "google_maps_url":"https://www.google.com/maps/search/?api=1&query=..."
}
404 when postal code missing:
{ "error":"zipcode_not_found", "message":"Postal code not found for the given input" }
Other errors:
- 400 { "error":"bad_request", "message": "..." }
- 500 { "error":"server_error", "message": "Internal server error" }

Setup
1) Clone the repo
2) Copy env template and fill keys
   cp .env.example .env
   Required: GOOGLE_API_KEY (enable Geocoding API + Static Maps. Lock by HTTP referrer if using a server, for edge functions use IP restrictions if available.)
   Optional: GEONAMES_USERNAME, POSITIONSTACK_KEY
3) Configure Supabase Edge Function secrets (in your Supabase project):
   - GOOGLE_API_KEY, GEONAMES_USERNAME, POSITIONSTACK_KEY
4) Start the app
   - Dev: npm i && npm run dev
   - Docker (production build): docker-compose up --build

Testing
- Frontend: npx vitest run
- Edge Functions (Deno): deno test -A supabase/functions

CI
- GitHub Actions workflow runs: lint/build/tests for frontend + Deno tests

Security notes
- Secrets never leave the backend; Static Maps images are proxied and returned as data URLs
- Standardized error JSON; no partial success without a ZIP
- External calls include retries with exponential backoff

How to extend country -> continent mapping
- Edit supabase/functions/_shared/continents.ts and add ISO2 codes
- Optionally set USE_RESTCOUNTRIES=true and implement a REST Countries call in the edge function (left as an exercise)

Example curl
curl -X POST \
  https://YOUR_SUPABASE_FUNCTIONS_URL/zipsearch \
  -H "Content-Type: application/json" \
  -d '{"query":"1600 Amphitheatre Parkway, Mountain View, CA"}'

Deployment
- Frontend: Build and host the static files (Dockerfile included uses Nginx). Suitable for Vercel/Netlify/Cloudflare Pages as well.
- Backend: Deploy Supabase Edge Function zipsearch and configure secrets in Supabase settings.

Privacy
“We only use your query to fetch public location data. We do not store personal data by default.”
