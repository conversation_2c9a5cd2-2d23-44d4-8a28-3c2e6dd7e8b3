import type { SearchResponse } from "@/types/api";
import { supabase } from "@/integrations/supabase/client";

function isLatLng(input: string): boolean {
  return /^-?\d{1,3}(\.\d+)?,\s*-?\d{1,3}(\.\d+)?$/.test(input.trim());
}

function mapsUrl(lat: number, lng: number) {
  return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
}

async function demoResolve(query: string): Promise<SearchResponse> {
  const q = query.trim().toLowerCase();

  const records: Record<string, SearchResponse> = {
    "eiffel tower": {
      zip: "75007",
      place_name: "Eiffel Tower, Paris",
      lat: 48.8584,
      lng: 2.2945,
      state: "Île-de-France",
      country: { name: "France", iso2: "FR" },
      continent: "Europe",
      image_url: "/placeholder.svg",
      google_maps_url: mapsUrl(48.8584, 2.2945),
    },
    "1600 amphitheatre pkwy, mountain view": {
      zip: "94043",
      place_name: "Googleplex, Mountain View",
      lat: 37.422,
      lng: -122.084,
      state: "California",
      country: { name: "United States", iso2: "US" },
      continent: "North America",
      image_url: "/placeholder.svg",
      google_maps_url: mapsUrl(37.422, -122.084),
    },
    "new york 10001": {
      zip: "10001",
      place_name: "New York, NY 10001",
      lat: 40.7506,
      lng: -73.9972,
      state: "New York",
      country: { name: "United States", iso2: "US" },
      continent: "North America",
      image_url: "/placeholder.svg",
      google_maps_url: mapsUrl(40.7506, -73.9972),
    },
  };

  if (records[q]) return records[q];

  // coordinate queries -> return a generic demo response
  if (isLatLng(q)) {
    const [latStr, lngStr] = q.split(",");
    const lat = parseFloat(latStr);
    const lng = parseFloat(lngStr);
    return {
      zip: "00000",
      place_name: `Sample Location (${lat.toFixed(4)}, ${lng.toFixed(4)})`,
      lat,
      lng,
      state: "",
      country: { name: "Unknown", iso2: "" },
      continent: "Unknown",
      image_url: "/placeholder.svg",
      google_maps_url: mapsUrl(lat, lng),
    };
  }

  const err: any = new Error("Postal code not found for the given input");
  err.status = 404;
  err.payload = { error: "zipcode_not_found", message: err.message };
  throw err;
}

export async function searchZip(query: string): Promise<SearchResponse> {
  try {
    const { data, error } = await supabase.functions.invoke("zipsearch", {
      body: { query },
    });

    if (error) {
      // No backend or function error -> fallback to demo
      return await demoResolve(query);
    }

    return data as SearchResponse;
  } catch (e) {
    // Network/unknown error -> demo fallback
    try {
      return await demoResolve(query);
    } catch (fallbackErr) {
      throw fallbackErr;
    }
  }
}
