import { describe, expect, it } from 'vitest';
import { searchZip } from '@/services/api';

// These tests rely on the stubbed Supabase client (no VITE_SUPABASE_URL/ANON set)
// and should exercise the local demo fallback logic.

describe('searchZip demo fallback', () => {
  it('returns demo result for known address example', async () => {
    const res = await searchZip('Eiffel Tower');
    expect(res.zip).toBe('75007');
    expect(res.country.iso2).toBe('FR');
    expect(res.google_maps_url).toContain('https://www.google.com/maps/search/');
  });

  it('returns demo result for coordinates', async () => {
    const res = await searchZip('37.422,-122.084');
    expect(res.zip).toBe('00000');
    expect(res.place_name).toContain('Sample Location');
  });
});

