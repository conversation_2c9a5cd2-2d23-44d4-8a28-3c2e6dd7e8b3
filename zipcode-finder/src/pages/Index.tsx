import { useState } from "react";
import { ZipSearchForm } from "@/components/ZipSearchForm";
import { ZipResultCard } from "@/components/ZipResultCard";
import { ZipError } from "@/components/ZipError";
import { searchZip } from "@/services/api";
import type { SearchResponse } from "@/types/api";
import { Skeleton } from "@/components/ui/skeleton";
import { Hero } from "@/components/Hero";
import { ExampleChips } from "@/components/ExampleChips";

const Index = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<SearchResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async (query: string) => {
    setLoading(true);
    setError(null);
    setResult(null);
    try {
      const data = await searchZip(query);
      setResult(data);
    } catch (e: any) {
      setError(e?.payload?.message || e?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="min-h-screen">
      <section className="container mx-auto py-16">
        <Hero />

        <ZipSearchForm onSubmit={handleSearch} loading={loading} />
        <ExampleChips onPick={handleSearch} disabled={loading} className="mt-4" />

        <div className="mt-10 max-w-4xl mx-auto space-y-6">
          {loading && (
            <div className="space-y-4 animate-fade-in">
              <Skeleton className="h-10 w-40 mx-auto" />
              <Skeleton className="h-64 w-full rounded-lg" />
            </div>
          )}
          {error && !loading && <ZipError message={error} />}
          {result && !loading && <ZipResultCard result={result} />}
        </div>

        <aside className="text-center text-xs text-muted-foreground mt-8">
          We only use your query to fetch public location data. We do not store personal data by default.
        </aside>
      </section>
    </main>
  );
};

export default Index;
