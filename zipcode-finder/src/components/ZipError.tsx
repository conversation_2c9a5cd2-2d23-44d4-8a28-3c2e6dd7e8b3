import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ZipErrorProps {
  code?: string;
  message: string;
}

export const ZipError = ({ code, message }: ZipErrorProps) => {
  return (
    <Alert variant="destructive" role="alert" aria-live="assertive">
      <AlertTitle>We couldn't find a postal code</AlertTitle>
      <AlertDescription>
        {message}
        <ul className="mt-2 list-disc pl-5 space-y-1">
          <li>Try a more specific address (include city and country)</li>
          <li>Provide coordinates like: 37.422,-122.084</li>
          <li>Check spelling and abbreviations</li>
        </ul>
      </AlertDescription>
    </Alert>
  );
};
