import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import type { SearchResponse } from "@/types/api";
import { useState } from "react";

interface ZipResultCardProps {
  result: SearchResponse;
}

export const ZipResultCard = ({ result }: ZipResultCardProps) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(result.zip);
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    } catch {}
  };

  return (
    <Card className="overflow-hidden transition-transform duration-300 hover:translate-y-0.5">
      <CardHeader>
        <CardTitle className="text-3xl sm:text-4xl font-bold tracking-tight">
          {result.zip}
        </CardTitle>
        <p className="text-muted-foreground">
          {result.place_name} • {result.state} • {result.country.name} ({result.country.iso2}) • {result.continent}
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 sm:grid-cols-2">
          <div className="space-y-2 order-2 sm:order-1">
            <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
              <dt className="text-muted-foreground">Latitude</dt>
              <dd>{result.lat.toFixed(6)}</dd>
              <dt className="text-muted-foreground">Longitude</dt>
              <dd>{result.lng.toFixed(6)}</dd>
              <dt className="text-muted-foreground">State</dt>
              <dd>{result.state}</dd>
              <dt className="text-muted-foreground">Country</dt>
              <dd>
                {result.country.name} ({result.country.iso2})
              </dd>
              <dt className="text-muted-foreground">Continent</dt>
              <dd>{result.continent}</dd>
            </dl>
            <div className="flex gap-3 pt-3">
              <Button variant="secondary" onClick={handleCopy} aria-label="Copy ZIP code">
                {copied ? "Copied" : "Copy ZIP"}
              </Button>
              <a
                className="inline-flex"
                href={result.google_maps_url}
                target="_blank"
                rel="noreferrer noopener"
                aria-label="Open in Google Maps"
              >
                <Button variant="hero">Open in Google Maps</Button>
              </a>
            </div>
          </div>
          <div className="order-1 sm:order-2">
            <img
              src={result.image_url}
              alt={`Map or photo of ${result.place_name}, ${result.state}, ${result.country.name}`}
              loading="lazy"
              className="w-full h-full object-cover rounded-md"
            />
            <p className="mt-2 text-xs text-muted-foreground">
              Image via Google Maps Static Maps
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
