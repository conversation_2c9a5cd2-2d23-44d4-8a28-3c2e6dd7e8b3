import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ZipSearchFormProps {
  onSubmit: (query: string) => void;
  loading?: boolean;
}

export const ZipSearchForm = ({ onSubmit, loading }: ZipSearchFormProps) => {
  const [query, setQuery] = useState("");

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        if (!query.trim()) return;
        onSubmit(query.trim());
      }}
      className="w-full max-w-2xl mx-auto flex gap-3"
      aria-label="Search for postal or ZIP codes by place, address, or coordinates"
    >
      <Input
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Try: 1600 Amphitheatre Pkwy or 37.422,-122.084"
        disabled={loading}
        aria-label="Search input"
        className="h-12"
      />
      <Button type="submit" disabled={loading} variant="hero" size="xl" aria-label="Search">
        {loading ? "Searching..." : "Search"}
      </Button>
    </form>
  );
};
