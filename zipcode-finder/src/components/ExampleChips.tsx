import { Button } from "@/components/ui/button";

interface ExampleChipsProps {
  onPick: (query: string) => void;
  disabled?: boolean;
  className?: string;
}

const EXAMPLES = [
  "1600 Amphitheatre Pkwy, Mountain View",
  "Eiffel Tower",
  "New York 10001",
  "37.422,-122.084",
];

export const ExampleChips = ({ onPick, disabled, className }: ExampleChipsProps) => {
  return (
    <div className={`w-full max-w-2xl mx-auto flex flex-wrap gap-2 justify-center md:justify-start ${className || ""}`}>
      {EXAMPLES.map((q) => (
        <Button
          key={q}
          type="button"
          variant="secondary"
          size="sm"
          disabled={disabled}
          onClick={() => onPick(q)}
          className="hover-scale"
          aria-label={`Try example: ${q}`}
        >
          {q}
        </Button>
      ))}
    </div>
  );
};
