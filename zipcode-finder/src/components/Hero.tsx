import heroImage from "@/assets/hero-zipfinder.jpg";

export const Hero = () => {
  return (
    <section className="container mx-auto mb-10 animate-fade-in">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
        <header className="space-y-4 text-center md:text-left">
          <h1 className="text-4xl sm:text-5xl font-bold leading-tight">
            Find postal/ZIP code for any place worldwide
          </h1>
          <p className="text-muted-foreground max-w-prose mx-auto md:mx-0">
            Enter an address, place name, or coordinates. We return the ZIP code, state, country, continent, and a map image.
          </p>
        </header>
        <div className="relative">
          <img
            src={heroImage}
            alt="World map with location pin — ZipFinder hero"
            loading="lazy"
            className="w-full h-auto rounded-xl shadow-xl hover-scale"
          />
        </div>
      </div>
    </section>
  );
};
