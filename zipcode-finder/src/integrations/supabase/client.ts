// Supabase client wrapper. Uses env in dev/prod; falls back to a no-op stub in demo mode.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

// Configure via Vite env. Do NOT hardcode secrets here.
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL as string | undefined;
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY as
  | string
  | undefined;

// Minimal shape we rely on in the app (only functions.invoke is used)
type MinimalSupabase = {
  functions: {
    invoke: (
      fn: string,
      opts?: { body?: unknown; headers?: Record<string, string> }
    ) => Promise<{ data: unknown; error: { message: string } | null }>;
  };
};

let client: MinimalSupabase;

if (SUPABASE_URL && SUPABASE_PUBLISHABLE_KEY) {
  // Browser-only storage; in Vitest/node we don't create the client
  client = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
    auth: {
      storage:
        typeof window !== "undefined"
          ? window.localStorage
          : (undefined as any),
      persistSession: true,
      autoRefreshToken: true,
    },
  }) as unknown as MinimalSupabase;
} else {
  // Demo mode: return an error so the API layer falls back to local demoResolve
  if (typeof console !== "undefined") {
    console.warn(
      "[ZipFinder] Supabase env not configured (VITE_SUPABASE_URL/ANON). Running in demo mode."
    );
  }
  client = {
    functions: {
      async invoke() {
        return { data: null, error: { message: "Supabase not configured" } };
      },
    },
  } as MinimalSupabase;
}

export const supabase = client;
