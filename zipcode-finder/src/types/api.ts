export interface CountryInfo {
  name: string;
  iso2: string;
}

export interface SearchResponse {
  zip: string;
  place_name: string;
  lat: number;
  lng: number;
  state: string;
  country: CountryInfo;
  continent: string;
  image_url: string; // May be a data URL (image/png)
  google_maps_url: string;
}

export interface ApiError {
  error: "zipcode_not_found" | "bad_request" | "server_error";
  message: string;
}
