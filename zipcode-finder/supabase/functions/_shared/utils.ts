// Shared utilities for Supabase Edge Functions

export type AddressComponent = {
  long_name: string;
  short_name: string;
  types: string[];
};

export type GoogleGeocodeResult = {
  formatted_address: string;
  place_id?: string;
  geometry: { location: { lat: number; lng: number } };
  address_components: AddressComponent[];
};

export function extractFromComponents(components: AddressComponent[]) {
  let postal = "";
  let state = "";
  let countryName = "";
  let countryCode = "";

  for (const c of components) {
    if (c.types.includes("postal_code") || c.types.includes("postal_code_prefix")) {
      postal = c.long_name;
    }
    if (c.types.includes("administrative_area_level_1")) {
      state = c.long_name;
    }
    if (c.types.includes("country")) {
      countryName = c.long_name;
      countryCode = c.short_name;
    }
  }

  return { postal, state, countryName, countryCode };
}

export async function fetchJsonWithRetry<T>(
  url: string,
  init?: RequestInit,
  retries = 2,
  backoffMs = 350
): Promise<T> {
  let lastErr: any;
  for (let i = 0; i <= retries; i++) {
    try {
      const res = await fetch(url, init);
      if (!res.ok) throw new Error(`HTTP ${res.status}`);
      return (await res.json()) as T;
    } catch (e) {
      lastErr = e;
      if (i === retries) break;
      await new Promise((r) => setTimeout(r, backoffMs * Math.pow(2, i)));
    }
  }
  throw lastErr;
}

export class LRUCache<V> {
  private max: number;
  private ttl: number;
  private map = new Map<string, { v: V; exp: number }>();

  constructor(max = 200, ttlMs = 1000 * 60 * 60 * 24) {
    this.max = max;
    this.ttl = ttlMs;
  }

  get(key: string): V | undefined {
    const hit = this.map.get(key);
    if (!hit) return undefined;
    if (Date.now() > hit.exp) {
      this.map.delete(key);
      return undefined;
    }
    // refresh recency
    this.map.delete(key);
    this.map.set(key, hit);
    return hit.v;
  }

  set(key: string, value: V) {
    if (this.map.size >= this.max) {
      const oldest = this.map.keys().next().value as string | undefined;
      if (oldest) this.map.delete(oldest);
    }
    this.map.set(key, { v: value, exp: Date.now() + this.ttl });
  }
}

export function googleMapsUrl(lat: number, lng: number, placeId?: string) {
  const base = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
  return placeId ? `${base}&query_place_id=${placeId}` : base;
}
