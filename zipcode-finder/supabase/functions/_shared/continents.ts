// Minimal ISO2 -> Continent mapping (extend as needed)
export const continentByCountry: Record<string, string> = {
  US: "North America",
  CA: "North America",
  MX: "North America",
  BR: "South America",
  AR: "South America",
  CL: "South America",
  CO: "South America",
  PE: "South America",
  GB: "Europe",
  IE: "Europe",
  FR: "Europe",
  DE: "Europe",
  IT: "Europe",
  ES: "Europe",
  PT: "Europe",
  NL: "Europe",
  BE: "Europe",
  LU: "Europe",
  CH: "Europe",
  AT: "Europe",
  SE: "Europe",
  NO: "Europe",
  FI: "Europe",
  DK: "Europe",
  PL: "Europe",
  CZ: "Europe",
  SK: "Europe",
  HU: "Europe",
  RO: "Europe",
  BG: "Europe",
  GR: "Europe",
  HR: "Europe",
  SI: "Europe",
  EE: "Europe",
  LV: "Europe",
  LT: "Europe",
  UA: "Europe",
  RU: "Europe",
  TR: "Asia",
  SA: "Asia",
  AE: "Asia",
  IL: "Asia",
  IN: "Asia",
  CN: "Asia",
  JP: "Asia",
  KR: "Asia",
  SG: "Asia",
  MY: "Asia",
  TH: "Asia",
  VN: "Asia",
  PH: "Asia",
  ID: "Asia",
  PK: "Asia",
  AU: "Oceania",
  NZ: "Oceania",
  ZA: "Africa",
  NG: "Africa",
  EG: "Africa",
  KE: "Africa",
  MA: "Africa",
  DZ: "Africa",
  TN: "Africa",
  ET: "Africa",
  GH: "Africa",
  CM: "Africa",
  SN: "Africa",
  CN: "Asia",
  HK: "Asia",
  TW: "Asia",
  IS: "Europe",
};

export function getContinent(iso2: string): string {
  return continentByCountry[iso2] || "Unknown";
}