export type SearchPayload = {
  query: string;
};

export type SearchResponse = {
  zip: string;
  place_name: string;
  lat: number;
  lng: number;
  state: string;
  country: { name: string; iso2: string };
  continent: string;
  image_url: string; // data URL
  google_maps_url: string;
};

export type ErrorResponse = {
  error: "zipcode_not_found" | "bad_request" | "server_error";
  message: string;
};
