import { extractFromComponents } from "../_shared/utils.ts";
import { assertEquals } from "https://deno.land/std@0.224.0/assert/mod.ts";

Deno.test("extracts postal, state, and country from components", () => {
  const comps = [
    { long_name: "94043", short_name: "94043", types: ["postal_code"] },
    { long_name: "California", short_name: "CA", types: ["administrative_area_level_1"] },
    { long_name: "United States", short_name: "US", types: ["country"] },
  ];
  const r = extractFromComponents(comps as any);
  assertEquals(r.postal, "94043");
  assertEquals(r.state, "California");
  assertEquals(r.countryCode, "US");
});
