// deno-lint-ignore-file no-explicit-any
import { extract<PERSON>rom<PERSON>omponents, fetchJsonWithRetry, googleMapsUrl, LRUCache } from "../_shared/utils.ts";
import type { ErrorResponse, SearchPayload, SearchResponse } from "../_shared/types.ts";
import type { GoogleGeocodeResult } from "../_shared/utils.ts";
import { getContinent } from "../_shared/continents.ts";

const GOOGLE_API_KEY = Deno.env.get("GOOGLE_API_KEY");
const GEONAMES_USERNAME = Deno.env.get("GEONAMES_USERNAME");
const POSITIONSTACK_KEY = Deno.env.get("POSITIONSTACK_KEY");

if (!GOOGLE_API_KEY) {
  console.warn("GOOGLE_API_KEY not set. zipsearch will fail until configured.");
}

const cache = new LRUCache<SearchResponse>(300, 1000 * 60 * 60 * 24); // 24h TTL

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

function jsonResponse(body: unknown, status = 200) {
  return new Response(JSON.stringify(body), {
    status,
    headers: {
      ...corsHeaders,
      "content-type": "application/json; charset=utf-8",
      "cache-control": "no-store",
    },
  });
}

function isLatLng(input: string): boolean {
  return /^-?\d{1,3}(\.\d+)?,\s*-?\d{1,3}(\.\d+)?$/.test(input);
}

async function googleGeocode(query: string): Promise<GoogleGeocodeResult | null> {
  if (!GOOGLE_API_KEY) throw new Error("Missing GOOGLE_API_KEY");
  const url = isLatLng(query)
    ? `https://maps.googleapis.com/maps/api/geocode/json?latlng=${encodeURIComponent(query)}&key=${GOOGLE_API_KEY}`
    : `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(query)}&key=${GOOGLE_API_KEY}`;

  const data = await fetchJsonWithRetry<any>(url);
  if (data.status !== "OK" || !data.results?.length) return null;
  return data.results[0] as GoogleGeocodeResult;
}

async function fallbackPostal(lat: number, lng: number): Promise<string | null> {
  // Try GeoNames
  if (GEONAMES_USERNAME) {
    try {
      const url = `https://secure.geonames.org/findNearbyPostalCodesJSON?lat=${lat}&lng=${lng}&maxRows=1&username=${GEONAMES_USERNAME}`;
      const data = await fetchJsonWithRetry<any>(url);
      const pc = data?.postalCodes?.[0]?.postalCode;
      if (pc) return String(pc);
    } catch (_) {}
  }

  // Try Positionstack
  if (POSITIONSTACK_KEY) {
    try {
      const url = `http://api.positionstack.com/v1/reverse?access_key=${POSITIONSTACK_KEY}&query=${lat},${lng}&limit=1`;
      const data = await fetchJsonWithRetry<any>(url);
      const pc = data?.data?.[0]?.postal_code;
      if (pc) return String(pc);
    } catch (_) {}
  }

  return null;
}

async function staticMapDataUrl(lat: number, lng: number): Promise<string> {
  if (!GOOGLE_API_KEY) throw new Error("Missing GOOGLE_API_KEY");
  const mapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${lat},${lng}&zoom=14&size=800x400&maptype=roadmap&markers=color:red%7C${lat},${lng}&scale=2&key=${GOOGLE_API_KEY}`;
  const res = await fetch(mapUrl);
  if (!res.ok) throw new Error("Failed to fetch static map");
  const buf = new Uint8Array(await res.arrayBuffer());
  let binary = "";
  for (let i = 0; i < buf.byteLength; i++) binary += String.fromCharCode(buf[i]);
  const base64 = btoa(binary);
  return `data:image/png;base64,${base64}`;
}

Deno.serve(async (req) => {
  // CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  try {
    if (req.method !== "POST") {
      return jsonResponse({ error: "bad_request", message: "Use POST" } satisfies ErrorResponse, 400);
    }

    const { query } = (await req.json()) as SearchPayload;
    if (!query || typeof query !== "string" || !query.trim()) {
      return jsonResponse(
        { error: "bad_request", message: "Query is required" } satisfies ErrorResponse,
        400
      );
    }

    const normalized = query.trim();

    const cached = cache.get(normalized);
    if (cached) {
      return jsonResponse(cached, 200);
    }

    const geo = await googleGeocode(normalized);
    if (!geo) {
      return jsonResponse(
        {
          error: "zipcode_not_found",
          message: "Postal code not found for the given input",
        } satisfies ErrorResponse,
        404
      );
    }

    const { lat, lng } = geo.geometry.location;
    const { postal, state, countryName, countryCode } = extractFromComponents(
      geo.address_components
    );

    let zip = postal;
    if (!zip) {
      const fb = await fallbackPostal(lat, lng);
      if (fb) zip = fb;
    }

    if (!zip) {
      return jsonResponse(
        {
          error: "zipcode_not_found",
          message: "Postal code not found for the given input",
        } satisfies ErrorResponse,
        404
      );
    }

    const continent = getContinent(countryCode);
    const image_url = await staticMapDataUrl(lat, lng);
    const google_maps_url = googleMapsUrl(lat, lng, geo.place_id);

    const payload: SearchResponse = {
      zip,
      place_name: geo.formatted_address,
      lat,
      lng,
      state,
      country: { name: countryName || countryCode, iso2: countryCode },
      continent,
      image_url,
      google_maps_url,
    };

    cache.set(normalized, payload);

    return jsonResponse(payload, 200);
  } catch (e: any) {
    console.error("zipsearch error", e?.message || e);
    return jsonResponse(
      { error: "server_error", message: "Internal server error" } satisfies ErrorResponse,
      500
    );
  }
});
